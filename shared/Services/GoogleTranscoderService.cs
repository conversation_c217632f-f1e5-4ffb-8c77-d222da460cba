using Google.Cloud.Video.Transcoder.V1;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;

namespace VidCompressor.Services;

public class GoogleTranscoderService
{
    private readonly TranscoderServiceClient _transcoderClient;
    private readonly GoogleCloudConfig _config;
    private readonly ILogger<GoogleTranscoderService> _logger;

    public GoogleTranscoderService(
        IOptions<GoogleCloudConfig> config,
        ILogger<GoogleTranscoderService> logger)
    {
        _config = config.Value;
        _logger = logger;
        
        // Initialize the transcoder client with service account credentials
        _transcoderClient = TranscoderServiceClient.Create();
    }

    /// <summary>
    /// Creates a transcoding job for video compression
    /// </summary>
    /// <param name="inputPath">The GCS path to the input video</param>
    /// <param name="outputPath">The GCS path for the output video</param>
    /// <param name="quality">The compression quality setting</param>
    /// <returns>A tuple containing the job name and the actual output path that will be created</returns>
    public async Task<(string jobName, string actualOutputPath)> CreateTranscodingJobAsync(string inputPath, string outputPath, string quality)
    {
        try
        {
            var preset = _config.Transcoder.Presets.GetValueOrDefault(quality)
                ?? _config.Transcoder.Presets["medium"];

            _logger.LogInformation("Creating transcoding job: Input={InputPath}, Output={OutputPath}, Quality={Quality}",
                inputPath, outputPath, quality);

            var parent = $"projects/{_config.ProjectId}/locations/{_config.Transcoder.Location}";
            var outputDirectory = GetOutputDirectory(outputPath);

            _logger.LogInformation("Transcoder job config: OutputDirectory={OutputDirectory}", outputDirectory);

            var jobConfig = CreateJobConfig(preset, inputPath, outputPath);
            var muxStreamKey = jobConfig.MuxStreams.First().Key;

            // Calculate the actual output path that will be created by the transcoder
            var actualOutputPath = $"{outputDirectory.TrimEnd('/')}/{muxStreamKey}.mp4";

            _logger.LogInformation("Transcoder job details: ExpectedOutput={ExpectedOutput}, MuxStreamKey={MuxStreamKey}, ActualOutput={ActualOutput}",
                outputPath, muxStreamKey, actualOutputPath);

            var job = new Job
            {
                InputUri = inputPath,
                OutputUri = outputDirectory,
                Config = jobConfig
            };

            var response = await _transcoderClient.CreateJobAsync(parent, job);

            _logger.LogInformation("Successfully created transcoding job: {JobName}, Actual output will be: {ActualOutputPath}",
                response.Name, actualOutputPath);
            return (response.Name, actualOutputPath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create transcoding job");
            throw;
        }
    }

    /// <summary>
    /// Gets the status of a transcoding job
    /// </summary>
    /// <param name="jobName">The job name returned from CreateTranscodingJobAsync</param>
    /// <returns>The current job status</returns>
    public async Task<TranscodingJobStatus> GetJobStatusAsync(string jobName)
    {
        try
        {
            var job = await _transcoderClient.GetJobAsync(jobName);
            
            var status = job.State switch
            {
                Job.Types.ProcessingState.Pending => TranscodingJobStatus.Pending,
                Job.Types.ProcessingState.Running => TranscodingJobStatus.Running,
                Job.Types.ProcessingState.Succeeded => TranscodingJobStatus.Succeeded,
                Job.Types.ProcessingState.Failed => TranscodingJobStatus.Failed,
                _ => TranscodingJobStatus.Unknown
            };

            _logger.LogDebug("Job {JobName} status: {Status}", jobName, status);
            return status;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get job status for: {JobName}", jobName);
            throw;
        }
    }

    /// <summary>
    /// Gets detailed information about a transcoding job
    /// </summary>
    /// <param name="jobName">The job name returned from CreateTranscodingJobAsync</param>
    /// <returns>Detailed job information</returns>
    public async Task<TranscodingJobInfo> GetJobInfoAsync(string jobName)
    {
        try
        {
            var job = await _transcoderClient.GetJobAsync(jobName);
            
            return new TranscodingJobInfo
            {
                JobName = job.Name,
                Status = job.State switch
                {
                    Job.Types.ProcessingState.Pending => TranscodingJobStatus.Pending,
                    Job.Types.ProcessingState.Running => TranscodingJobStatus.Running,
                    Job.Types.ProcessingState.Succeeded => TranscodingJobStatus.Succeeded,
                    Job.Types.ProcessingState.Failed => TranscodingJobStatus.Failed,
                    _ => TranscodingJobStatus.Unknown
                },
                InputUri = job.InputUri,
                OutputUri = job.OutputUri,
                CreateTime = job.CreateTime?.ToDateTime(),
                StartTime = job.StartTime?.ToDateTime(),
                EndTime = job.EndTime?.ToDateTime(),
                ErrorMessage = job.Error?.Message,
                Progress = 0.0 // Progress tracking would need to be implemented separately
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get job info for: {JobName}", jobName);
            throw;
        }
    }

    /// <summary>
    /// Cancels a running transcoding job
    /// </summary>
    /// <param name="jobName">The job name to cancel</param>
    public async Task CancelJobAsync(string jobName)
    {
        try
        {
            _logger.LogInformation("Cancelling transcoding job: {JobName}", jobName);
            await _transcoderClient.DeleteJobAsync(jobName);
            _logger.LogInformation("Successfully cancelled transcoding job: {JobName}", jobName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to cancel job: {JobName}", jobName);
            throw;
        }
    }

    /// <summary>
    /// Creates the job configuration based on the compression preset
    /// </summary>
    /// <param name="preset">The transcoding preset with quality settings</param>
    /// <param name="inputPath">The input file path to derive unique naming from</param>
    /// <param name="outputPath">The output file path to derive unique naming from</param>
    private JobConfig CreateJobConfig(TranscoderPreset preset, string inputPath, string outputPath)
    {
        var config = new JobConfig();

        // Add input
        config.Inputs.Add(new Input
        {
            Key = "input0"
        });

        // Note: No Output configuration needed in JobConfig - Job.OutputUri handles this

        // Create elementary streams
        var videoStream = new ElementaryStream
        {
            Key = "video-stream0",
            VideoStream = new VideoStream
            {
                H264 = new VideoStream.Types.H264CodecSettings
                {
                    BitrateBps = preset.VideoBitrate,
                    FrameRate = 30,
                    HeightPixels = 1080,
                    WidthPixels = 1920
                }
            }
        };

        var audioStream = new ElementaryStream
        {
            Key = "audio-stream0",
            AudioStream = new AudioStream
            {
                Codec = "aac",
                BitrateBps = preset.AudioBitrate,
                ChannelCount = 2,
                SampleRateHertz = 48000
            }
        };

        config.ElementaryStreams.Add(videoStream);
        config.ElementaryStreams.Add(audioStream);

        // Generate unique mux stream key based on the output filename
        var muxStreamKey = GenerateUniqueMuxStreamKey(outputPath);

        // Create mux stream with unique key
        var muxStream = new MuxStream
        {
            Key = muxStreamKey,
            Container = "mp4"
        };
        muxStream.ElementaryStreams.Add("video-stream0");
        muxStream.ElementaryStreams.Add("audio-stream0");

        config.MuxStreams.Add(muxStream);

        return config;
    }

    /// <summary>
    /// Generates a unique mux stream key based on the output file path
    /// </summary>
    /// <param name="outputPath">The output file path</param>
    /// <returns>A unique mux stream key derived from the filename</returns>
    private static string GenerateUniqueMuxStreamKey(string outputPath)
    {
        try
        {
            // Extract filename from the output path (e.g., gs://bucket/path/filename.mp4 -> filename.mp4)
            var lastSlashIndex = outputPath.LastIndexOf('/');
            var filename = lastSlashIndex >= 0 ? outputPath.Substring(lastSlashIndex + 1) : outputPath;

            // Remove the .mp4 extension to get the base name
            var filenameWithoutExtension = Path.GetFileNameWithoutExtension(filename);

            // Replace any characters that might not be suitable for a mux stream key
            // Keep only alphanumeric characters, hyphens, and underscores
            var sanitizedKey = System.Text.RegularExpressions.Regex.Replace(filenameWithoutExtension, @"[^a-zA-Z0-9\-_]", "_");

            // Ensure the key is not empty and has a reasonable length
            if (string.IsNullOrEmpty(sanitizedKey) || sanitizedKey.Length > 50)
            {
                // Fallback to a timestamp-based key if sanitization results in an unusable key
                sanitizedKey = $"video_{DateTime.UtcNow:yyyyMMdd_HHmmss}";
            }

            return sanitizedKey;
        }
        catch
        {
            // Fallback to timestamp-based key if any error occurs
            return $"video_{DateTime.UtcNow:yyyyMMdd_HHmmss}";
        }
    }

    /// <summary>
    /// Extracts the output directory from a full output path
    /// </summary>
    private static string GetOutputDirectory(string outputPath)
    {
        // Convert gs://bucket/path/file.mp4 to gs://bucket/path/
        var lastSlashIndex = outputPath.LastIndexOf('/');
        return lastSlashIndex > 0 ? outputPath.Substring(0, lastSlashIndex + 1) : outputPath;
    }
}

public enum TranscodingJobStatus
{
    Unknown,
    Pending,
    Running,
    Succeeded,
    Failed
}

public class TranscodingJobInfo
{
    public string JobName { get; set; } = string.Empty;
    public TranscodingJobStatus Status { get; set; }
    public string InputUri { get; set; } = string.Empty;
    public string OutputUri { get; set; } = string.Empty;
    public DateTime? CreateTime { get; set; }
    public DateTime? StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public string? ErrorMessage { get; set; }
    public double Progress { get; set; }
}
