using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using System.Net.Http;
using System.Text.Json;
using System.Text.Json.Serialization;
using Google.Apis.Auth;
using VidCompressor.Models;

[ApiController]
[Route("api/[controller]")]
public class AuthController : ControllerBase
{
    private readonly ApplicationDbContext _context;
    private readonly IConfiguration _configuration;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ILogger<AuthController> _logger;
    private static readonly Dictionary<string, DateTime> _processedCodes = new();
    private static readonly object _lockObject = new();

    public AuthController(ApplicationDbContext context, IConfiguration configuration, IHttpClientFactory httpClientFactory, ILogger<AuthController> logger)
    {
        _context = context;
        _configuration = configuration;
        _httpClientFactory = httpClientFactory;
        _logger = logger;
    }

    [HttpGet("google-oauth-url")]
    [AllowAnonymous]
    public IActionResult GetGoogleOAuthUrl()
    {
        var clientId = _configuration["Google:ClientId"];
        var redirectUri = _configuration["Google:RedirectUri"] ?? throw new InvalidOperationException("Google RedirectUri not configured");
        var scopes = "openid email profile https://www.googleapis.com/auth/photoslibrary.appendonly https://www.googleapis.com/auth/photoslibrary.readonly https://www.googleapis.com/auth/photospicker.mediaitems.readonly";

        var authUrl = $"https://accounts.google.com/o/oauth2/v2/auth?" +
                     $"client_id={clientId}&" +
                     $"redirect_uri={Uri.EscapeDataString(redirectUri)}&" +
                     $"scope={Uri.EscapeDataString(scopes)}&" +
                     $"response_type=code&" +
                     $"access_type=offline&" +
                     $"prompt=consent";

        return Ok(new { authUrl });
    }

    [HttpGet("debug/processed-codes")]
    [AllowAnonymous]
    public IActionResult GetProcessedCodes()
    {
        lock (_lockObject)
        {
            var codes = _processedCodes.Select(kvp => new
            {
                Code = kvp.Key.Length > 10 ? kvp.Key.Substring(0, 10) + "..." : kvp.Key,
                ProcessedAt = kvp.Value
            }).ToList();

            return Ok(new { processedCodes = codes, count = codes.Count });
        }
    }

    [HttpPost("oauth-callback")]
    [AllowAnonymous]
    public async Task<IActionResult> HandleOAuthCallback([FromBody] OAuthCallbackRequest request)
    {
        var requestId = Guid.NewGuid().ToString("N")[..8]; // Short unique ID for this request

        // Check for duplicate requests using authorization code
        if (!string.IsNullOrEmpty(request.Code))
        {
            lock (_lockObject)
            {
                // Clean up old entries (older than 5 minutes)
                var cutoff = DateTime.UtcNow.AddMinutes(-5);
                var keysToRemove = _processedCodes.Where(kvp => kvp.Value < cutoff).Select(kvp => kvp.Key).ToList();
                foreach (var key in keysToRemove)
                {
                    _processedCodes.Remove(key);
                }

                // Check if this code has already been processed
                if (_processedCodes.ContainsKey(request.Code))
                {
                    _logger.LogWarning("[{RequestId}] Duplicate OAuth callback detected for code: {Code}", requestId, request.Code.Substring(0, 10) + "...");
                    return BadRequest(new { message = "This authorization code has already been processed" });
                }

                // Mark this code as being processed
                _processedCodes[request.Code] = DateTime.UtcNow;
            }
        }

        try
        {
            var clientId = _configuration["Google:ClientId"];
            var clientSecret = _configuration["Google:ClientSecret"];
            var redirectUri = _configuration["Google:RedirectUri"] ?? throw new InvalidOperationException("Google RedirectUri not configured");

            _logger.LogInformation("[{RequestId}] OAuth callback initiated for ClientId: {ClientId}", requestId, clientId);
            _logger.LogDebug("[{RequestId}] OAuth callback - Code: {Code}", requestId, request.Code?.Length > 20 ? request.Code.Substring(0, 20) + "..." : request.Code);
            _logger.LogDebug("[{RequestId}] OAuth callback - RedirectUri: {RedirectUri}", requestId, redirectUri);

            // Exchange authorization code for access token
            var httpClient = _httpClientFactory.CreateClient();
            var tokenRequest = new FormUrlEncodedContent(new[]
            {
                new KeyValuePair<string, string>("client_id", clientId ?? ""),
                new KeyValuePair<string, string>("client_secret", clientSecret ?? ""),
                new KeyValuePair<string, string>("code", request.Code ?? ""),
                new KeyValuePair<string, string>("grant_type", "authorization_code"),
                new KeyValuePair<string, string>("redirect_uri", redirectUri)
            });

            _logger.LogInformation("[{RequestId}] Sending token exchange request to Google", requestId);
            var tokenResponse = await httpClient.PostAsync("https://oauth2.googleapis.com/token", tokenRequest);
            var tokenContent = await tokenResponse.Content.ReadAsStringAsync();

            _logger.LogInformation("[{RequestId}] Token exchange response status: {StatusCode}", requestId, tokenResponse.StatusCode);
            if (!tokenResponse.IsSuccessStatusCode)
            {
                _logger.LogError("[{RequestId}] Token exchange failed with content: {Content}", requestId, tokenContent);
            }

            if (!tokenResponse.IsSuccessStatusCode)
            {
                return BadRequest(new { message = "Failed to exchange authorization code for tokens", error = tokenContent });
            }

            var tokenData = JsonSerializer.Deserialize<GoogleTokenResponse>(tokenContent, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            _logger.LogDebug("[{RequestId}] Parsed token data - ExpiresIn: {ExpiresIn}", requestId, tokenData?.ExpiresIn);

            // Extract user info from ID token instead of making API call
            _logger.LogDebug("[{RequestId}] Extracting user info from ID token", requestId);

            if (string.IsNullOrEmpty(tokenData?.IdToken))
            {
                return BadRequest(new { message = "No ID token received from Google" });
            }

            var userInfo = await ExtractUserInfoFromIdToken(tokenData.IdToken);
            if (userInfo == null)
            {
                return BadRequest(new { message = "Failed to extract user info from ID token" });
            }

            _logger.LogInformation("[{RequestId}] Extracted user info - ID: {UserId}, Email: {Email}", requestId, userInfo.Id, userInfo.Email);

            // Store user with access token and refresh token
            var user = await _context.Users.FirstOrDefaultAsync(u => u.Id == userInfo.Id);
            if (user == null)
            {
                user = new User
                {
                    Id = userInfo.Id,
                    Email = userInfo.Email,
                    SubscriptionStatus = "Free",
                    GoogleAccessToken = tokenData.AccessToken,
                    GoogleTokenExpiry = DateTime.UtcNow.AddSeconds(tokenData.ExpiresIn),
                    GoogleRefreshToken = tokenData.RefreshToken
                };
                _context.Users.Add(user);
            }
            else
            {
                user.GoogleAccessToken = tokenData.AccessToken;
                user.GoogleTokenExpiry = DateTime.UtcNow.AddSeconds(tokenData.ExpiresIn);
                // Only update refresh token if a new one is provided
                if (!string.IsNullOrEmpty(tokenData.RefreshToken))
                {
                    user.GoogleRefreshToken = tokenData.RefreshToken;
                }
                _context.Users.Update(user);
            }

            await _context.SaveChangesAsync();

            // Generate JWT token
            var jwtToken = GenerateJwtToken(user);
            _logger.LogInformation("[{RequestId}] OAuth callback completed successfully for user {UserId}", requestId, user.Id);
            return Ok(new { token = jwtToken });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{RequestId}] OAuth callback failed for request with code: {Code}", requestId, request.Code?.Substring(0, 10) + "...");
            return BadRequest(new { message = "OAuth callback failed", error = ex.Message });
        }
    }



    [HttpGet("me")]
    [Microsoft.AspNetCore.Authorization.Authorize]
    public async Task<IActionResult> GetCurrentUser()
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized(new { message = "User not authenticated" });
            }

            var user = await _context.Users.FirstOrDefaultAsync(u => u.Id == userId);
            if (user == null)
            {
                return NotFound(new { message = "User not found" });
            }

            return Ok(new
            {
                id = user.Id,
                email = user.Email,
                subscriptionStatus = user.SubscriptionStatus
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = "Error retrieving user: " + ex.Message });
        }
    }



    private async Task<GoogleUserInfo?> ExtractUserInfoFromIdToken(string idToken)
    {
        try
        {
            var payload = await GoogleJsonWebSignature.ValidateAsync(idToken, new GoogleJsonWebSignature.ValidationSettings
            {
                Audience = new[] { _configuration["Google:ClientId"] ?? throw new InvalidOperationException("Google ClientId not configured") }
            });

            if (payload == null)
            {
                _logger.LogError("ID token validation failed (payload is null)");
                return null;
            }

            _logger.LogInformation("Validated ID token payload - Sub: {Subject}, Email: {Email}", payload.Subject, payload.Email);

            return new GoogleUserInfo
            {
                Id = payload.Subject,
                Email = payload.Email,
                Name = payload.Name,
                Picture = payload.Picture
            };
        }
        catch (InvalidJwtException ex)
        {
            _logger.LogError(ex, "Invalid ID token");
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting user info from ID token");
            return null;
        }
    }

    private async Task<string?> RefreshGoogleAccessToken(User user)
    {
        if (string.IsNullOrEmpty(user.GoogleRefreshToken))
        {
            _logger.LogWarning("No refresh token found for user {UserId}. User needs to re-authenticate", user.Id);
            return null; // User needs to re-authenticate
        }

        var httpClient = _httpClientFactory.CreateClient();
        var tokenRequest = new FormUrlEncodedContent(new[]
        {
            new KeyValuePair<string, string>("client_id", _configuration["Google:ClientId"] ?? ""),
            new KeyValuePair<string, string>("client_secret", _configuration["Google:ClientSecret"] ?? ""),
            new KeyValuePair<string, string>("refresh_token", user.GoogleRefreshToken),
            new KeyValuePair<string, string>("grant_type", "refresh_token")
        });

        var tokenResponse = await httpClient.PostAsync("https://oauth2.googleapis.com/token", tokenRequest);
        var tokenContent = await tokenResponse.Content.ReadAsStringAsync();

        if (!tokenResponse.IsSuccessStatusCode)
        {
            _logger.LogError("Failed to refresh token for user {UserId}: {Error}", user.Id, tokenContent);
            // Invalidate the stored refresh token if it consistently fails
            user.GoogleRefreshToken = null; // Mark as needing re-auth
            await _context.SaveChangesAsync();
            return null;
        }

        var tokenData = JsonSerializer.Deserialize<GoogleTokenResponse>(tokenContent, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        if (tokenData != null && !string.IsNullOrEmpty(tokenData.AccessToken))
        {
            user.GoogleAccessToken = tokenData.AccessToken;
            user.GoogleTokenExpiry = DateTime.UtcNow.AddSeconds(tokenData.ExpiresIn);
            // The refresh token is often not returned on refresh, but if it is, update it.
            if (!string.IsNullOrEmpty(tokenData.RefreshToken)) {
                user.GoogleRefreshToken = tokenData.RefreshToken;
            }
            await _context.SaveChangesAsync();
            return tokenData.AccessToken;
        }
        return null;
    }

    private string GenerateJwtToken(User user)
    {
        var tokenHandler = new JwtSecurityTokenHandler();
        var key = Encoding.ASCII.GetBytes(_configuration["Jwt:Secret"] ?? throw new InvalidOperationException("JWT Secret not configured"));
        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(new[]
            {
                new Claim(ClaimTypes.NameIdentifier, user.Id),
                new Claim(ClaimTypes.Email, user.Email)
            }),
            Expires = DateTime.UtcNow.AddDays(7),
            SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
        };
        var token = tokenHandler.CreateToken(tokenDescriptor);
        return tokenHandler.WriteToken(token);
    }
}

public class OAuthCallbackRequest
{
    public string Code { get; set; } = string.Empty;
}


