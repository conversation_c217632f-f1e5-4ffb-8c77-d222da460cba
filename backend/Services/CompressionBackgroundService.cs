using Microsoft.EntityFrameworkCore;
using VidCompressor.Models;
using VidCompressor.Services;
using System.Diagnostics;

namespace VidCompressor.Services;

public class CompressionBackgroundService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<CompressionBackgroundService> _logger;
    private static readonly ActivitySource Activity = new ActivitySource("VidCompressor.CompressionService");

    public CompressionBackgroundService(
        IServiceProvider serviceProvider,
        ILogger<CompressionBackgroundService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        // Monitor running transcoder jobs
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await MonitorTranscoderJobsAsync(stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in transcoder job monitoring");
            }

            await Task.Delay(TimeSpan.FromSeconds(30), stoppingToken); // Check every 30 seconds
        }
    }

    public async Task ProcessCompressionJobAsync(string jobId, CancellationToken cancellationToken = default)
    {
        using var activity = Activity.StartActivity("ProcessCompressionJob");

        _logger.LogInformation("Processing compression job: {JobId}", jobId);
        activity?.SetTag("job.id", jobId);

        try
        {
            await ProcessCompressionJobInternalAsync(jobId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process compression job: {JobId}", jobId);
            await MarkJobAsFailed(jobId, ex.Message);
        }
    }

    private async Task ProcessCompressionJobInternalAsync(string jobId, CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        var storageService = scope.ServiceProvider.GetRequiredService<GoogleCloudStorageService>();
        var transcoderService = scope.ServiceProvider.GetRequiredService<GoogleTranscoderService>();
        var googlePhotosService = scope.ServiceProvider.GetRequiredService<GooglePhotosService>();

        var job = await context.CompressionJobs.FirstOrDefaultAsync(j => j.Id == jobId, cancellationToken);
        if (job == null)
        {
            _logger.LogWarning("Compression job not found: {JobId}", jobId);
            return;
        }

        try
        {
            // Step 1: Download video from Google Photos
            await UpdateJobStatus(context, job, CompressionJobStatus.DownloadingFromGooglePhotos);
            
            var accessToken = await GetUserAccessToken(context, job.UserId);
            if (string.IsNullOrEmpty(accessToken))
            {
                throw new InvalidOperationException("Unable to get valid access token for user");
            }

            using var videoStream = await googlePhotosService.DownloadVideoAsync(accessToken, job.MediaItemId, job.BaseUrl);
            var originalSize = videoStream.Length;
            job.OriginalSizeBytes = originalSize;

            // Step 2: Upload to Cloud Storage
            await UpdateJobStatus(context, job, CompressionJobStatus.UploadingToStorage);
            
            var inputFileName = $"{job.MediaItemId}_{DateTime.UtcNow:yyyyMMdd_HHmmss}.mp4";
            var inputPath = await storageService.UploadToInputBucketAsync(videoStream, inputFileName);
            job.InputStoragePath = inputPath;

            // Step 3: Start transcoding
            await UpdateJobStatus(context, job, CompressionJobStatus.TranscodingInProgress);

            var outputPath = storageService.GenerateOutputPath(inputPath, job.Quality);

            var (transcoderJobName, actualOutputPath) = await transcoderService.CreateTranscodingJobAsync(inputPath, outputPath, job.Quality);
            job.TranscoderJobName = transcoderJobName;
            job.OutputStoragePath = actualOutputPath; // Use the actual path that will be created
            job.StartedAt = DateTime.UtcNow;

            await context.SaveChangesAsync(cancellationToken);
            
            _logger.LogInformation("Started transcoding job {TranscoderJobName} for compression job {JobId}", 
                transcoderJobName, jobId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start compression job: {JobId}", jobId);
            await MarkJobAsFailed(jobId, ex.Message);
        }
    }

    private async Task MonitorTranscoderJobsAsync(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        var transcoderService = scope.ServiceProvider.GetRequiredService<GoogleTranscoderService>();

        var runningJobs = await context.CompressionJobs
            .Where(j => j.Status == CompressionJobStatus.TranscodingInProgress &&
                       !string.IsNullOrEmpty(j.TranscoderJobName))
            .ToListAsync(cancellationToken);

        foreach (var job in runningJobs)
        {
            try
            {
                var status = await transcoderService.GetJobStatusAsync(job.TranscoderJobName!);

                if (status == TranscodingJobStatus.Succeeded)
                {
                    _logger.LogInformation("Transcoding completed for job {JobId}", job.Id);
                    await CompleteTranscodingAsync(job.Id, cancellationToken);
                }
                else if (status == TranscodingJobStatus.Failed)
                {
                    _logger.LogError("Transcoding failed for job {JobId}", job.Id);
                    var jobInfo = await transcoderService.GetJobInfoAsync(job.TranscoderJobName!);
                    await MarkJobAsFailed(job.Id, jobInfo.ErrorMessage ?? "Transcoding failed");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to check transcoder job status for {JobId}", job.Id);
            }
        }
    }

    private async Task CompleteTranscodingAsync(string jobId, CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        var storageService = scope.ServiceProvider.GetRequiredService<GoogleCloudStorageService>();
        var googlePhotosService = scope.ServiceProvider.GetRequiredService<GooglePhotosService>();

        var job = await context.CompressionJobs.FirstOrDefaultAsync(j => j.Id == jobId, cancellationToken);
        if (job == null) return;

        try
        {
            // Step 4: Download compressed video from storage
            await UpdateJobStatus(context, job, CompressionJobStatus.DownloadingFromStorage);

            _logger.LogInformation("Attempting to download compressed video from: {OutputStoragePath}", job.OutputStoragePath);

            // Check if the file exists before trying to download
            var fileExists = await storageService.FileExistsAsync(job.OutputStoragePath!);
            if (!fileExists)
            {
                throw new FileNotFoundException($"Compressed video file not found at: {job.OutputStoragePath}");
            }

            using var compressedVideoStream = await storageService.DownloadFromOutputBucketAsync(job.OutputStoragePath!);
            var compressedSize = compressedVideoStream.Length;
            job.CompressedSizeBytes = compressedSize;
            job.CompressionRatio = job.OriginalSizeBytes > 0 ? (double)compressedSize / job.OriginalSizeBytes : 0;

            // Step 5: Upload to Google Photos
            await UpdateJobStatus(context, job, CompressionJobStatus.UploadingToGooglePhotos);
            
            var accessToken = await GetUserAccessToken(context, job.UserId);
            if (string.IsNullOrEmpty(accessToken))
            {
                throw new InvalidOperationException("Unable to get valid access token for user");
            }

            // Save compressed video to temp file for upload
            var tempFilePath = Path.GetTempFileName();
            try
            {
                await using (var fileStream = new FileStream(tempFilePath, FileMode.Create))
                {
                    await compressedVideoStream.CopyToAsync(fileStream, cancellationToken);
                }

                await googlePhotosService.UploadVideoAsync(accessToken, tempFilePath);

                // Step 6: Delete original if requested
                if (job.OverwriteOriginal)
                {
                    await UpdateJobStatus(context, job, CompressionJobStatus.DeletingOriginal);
                    // Note: Google Photos API doesn't support deleting media items
                    // This would need to be handled differently or as a manual step
                }

                // Step 7: Complete the job
                await UpdateJobStatus(context, job, CompressionJobStatus.Completed);
                job.CompletedAt = DateTime.UtcNow;

                // Clean up storage files
                await CleanupStorageFiles(storageService, job);

                _logger.LogInformation("Compression job {JobId} completed successfully. " +
                    "Original size: {OriginalSize} bytes, Compressed size: {CompressedSize} bytes, " +
                    "Compression ratio: {CompressionRatio:P2}", 
                    jobId, job.OriginalSizeBytes, job.CompressedSizeBytes, job.CompressionRatio);
            }
            finally
            {
                if (File.Exists(tempFilePath))
                {
                    File.Delete(tempFilePath);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to complete compression job: {JobId}", jobId);
            await MarkJobAsFailed(jobId, ex.Message);
        }
    }

    private async Task UpdateJobStatus(ApplicationDbContext context, CompressionJob job, CompressionJobStatus status)
    {
        job.Status = status;
        await context.SaveChangesAsync();
        _logger.LogInformation("Updated job {JobId} status to {Status}", job.Id, status);
    }

    private async Task MarkJobAsFailed(string jobId, string errorMessage)
    {
        using var scope = _serviceProvider.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

        var job = await context.CompressionJobs.FirstOrDefaultAsync(j => j.Id == jobId);
        if (job != null)
        {
            job.Status = CompressionJobStatus.Failed;
            job.ErrorMessage = errorMessage;
            job.CompletedAt = DateTime.UtcNow;
            await context.SaveChangesAsync();
        }
    }

    private async Task<string?> GetUserAccessToken(ApplicationDbContext context, string userId)
    {
        var user = await context.Users.FirstOrDefaultAsync(u => u.Id == userId);
        if (user == null) return null;

        // Check if token is expired and refresh if needed
        if (user.GoogleTokenExpiry <= DateTime.UtcNow && !string.IsNullOrEmpty(user.GoogleRefreshToken))
        {
            // Token refresh logic would go here
            // For now, return the existing token
        }

        return user.GoogleAccessToken;
    }

    private async Task CleanupStorageFiles(GoogleCloudStorageService storageService, CompressionJob job)
    {
        try
        {
            if (!string.IsNullOrEmpty(job.InputStoragePath))
            {
                await storageService.DeleteFileAsync(job.InputStoragePath);
            }
            if (!string.IsNullOrEmpty(job.OutputStoragePath))
            {
                await storageService.DeleteFileAsync(job.OutputStoragePath);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to cleanup storage files for job {JobId}", job.Id);
        }
    }
}
